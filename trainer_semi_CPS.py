# Copyright 2020 - 2021 MONAI Consortium
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import shutil
import time
import gc
import numpy as np
import torch
import torch.nn.parallel
import torch.utils.data.distributed
from tensorboardX import SummaryWriter
from torch.cuda.amp import GradScaler, autocast
from utils.utils import distributed_all_gather

from monai.data import decollate_batch


def dice(x, y):
    intersect = np.sum(np.sum(np.sum(x * y)))
    y_sum = np.sum(np.sum(np.sum(y)))
    if y_sum == 0:
        return 0.0
    x_sum = np.sum(np.sum(np.sum(x)))
    return 2 * intersect / (x_sum + y_sum)


class AverageMeter(object):
    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = np.where(self.count > 0, self.sum / self.count, self.sum)

def sigmoid_rampup(current, rampup_length):
    """Exponential rampup from https://arxiv.org/abs/1610.02242"""
    if rampup_length == 0:
        return 1.0
    else:               
        current = np.clip(current, 0.0, rampup_length)
        phase = 1.0 - current / rampup_length
        return float(np.exp(-5.0 * phase * phase))

def get_current_consistency_weight(epoch):
    # Consistency ramp-up from https://arxiv.org/abs/1610.02242
    return 0.1 * sigmoid_rampup(epoch, 300)

def train_epoch(model, model2, loader_lab, loader_unlab, optimizer, optimizer2, scaler, epoch, loss_func, args):
    model.train()
    model2.train()
    start_time = time.time()
    run_loss = AverageMeter()
    
    consistency_weight = get_current_consistency_weight(epoch)
    
    for idx, (batch_data_lab, batch_data_unlab) in enumerate(zip(loader_lab, loader_unlab)):
        data_lab, target_lab = batch_data_lab["image"], batch_data_lab["label"]
        data_lab, target_lab = data_lab.cuda(args.rank), target_lab.cuda(args.rank)

        data_unlab = batch_data_unlab["image"]
        data_unlab = data_unlab.cuda(args.rank)
        data_combined = torch.cat([data_lab, data_unlab], dim=0)
        # print(data.shape)
        # print(batch_data_lab["image_meta_dict"]["filename_or_obj"])
        # input()
        for param in model.parameters():
            param.grad = None
        for param in model2.parameters():
            param.grad = None
        with autocast(enabled=args.amp):
            logits1 = model(data_combined)
            logits2 = model2(data_combined)
            logits1_soft = torch.softmax(logits1, dim=1)
            logits2_soft = torch.softmax(logits2, dim=1)
            pseudo_label1 = torch.argmax(logits1_soft[args.batch_size * args.num_samples:].detach(), dim=1, keepdim=True)
            pseudo_label2 = torch.argmax(logits2_soft[args.batch_size * args.num_samples:].detach(), dim=1, keepdim=True)
            loss1 = loss_func(logits1[:args.batch_size * args.num_samples], target_lab)
            loss2 = loss_func(logits2[:args.batch_size * args.num_samples], target_lab)
            pseudo_supervision1 = loss_func(logits1[args.batch_size * args.num_samples:], pseudo_label2)
            pseudo_supervision2 = loss_func(logits2[args.batch_size * args.num_samples:], pseudo_label1)
            loss = loss1 + loss2 + consistency_weight * (pseudo_supervision1 + pseudo_supervision2)
            print("loss1: {:.4f}, loss2: {:.4f}, loss_pseudo: {:.4f},".format(loss1.item(), loss2.item(), consistency_weight  * (pseudo_supervision1.item() + pseudo_supervision2.item())))
        if args.amp:
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.step(optimizer2)
            scaler.update()
        else:
            loss.backward()
            optimizer.step()
            optimizer2.step()
        if args.distributed:
            loss_list = distributed_all_gather([loss], out_numpy=True, is_valid=idx < loader_lab.sampler.valid_length)
            run_loss.update(
                np.mean(np.mean(np.stack(loss_list, axis=0), axis=0), axis=0), n=args.batch_size * args.world_size
            )
        else:
            run_loss.update(loss.item(), n=args.batch_size)
        
        # if idx % 200 == 0 and args.rank == 0:
        #     print(
        #         "Iteration {}/{}".format(idx, args.max_epochs * 5000),
        #         "loss: {:.4f}".format(run_loss.avg),
        #         "time {:.2f}s".format(time.time() - start_time),
        #     )
        
    if args.rank == 0:
        print(
            "Epoch {}/{}".format(epoch, args.max_epochs),
            "loss: {:.4f}".format(run_loss.avg),
            "time {:.2f}s".format(time.time() - start_time),
        )
    start_time = time.time()
    return run_loss.avg


def val_epoch(model, loader, epoch, acc_func, args, model_inferer=None, post_label=None, post_pred=None):
    model.eval()
    start_time = time.time()
    val_dice = []
    with torch.no_grad():
        for idx, batch_data in enumerate(loader):
            if isinstance(batch_data, list):
                data, target = batch_data
            else:
                data, target = batch_data["image"], batch_data["label"]
            data, target = data.cuda(args.rank), target.cuda(args.rank)
            img_name = batch_data["image_meta_dict"]["filename_or_obj"][0].split("/")[-1]
            with autocast(enabled=args.amp):
                if model_inferer is not None:
                    logits = model_inferer(data)
                else:
                    logits = model(data)
            if not logits.is_cuda:
                target = target.cpu()
            val_labels_list = decollate_batch(target)
            val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]
            val_outputs_list = decollate_batch(logits)
            val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]
            acc = acc_func(y_pred=val_output_convert, y=val_labels_convert)
            acc = acc.cuda(args.rank)

            if args.distributed:
                acc_list = distributed_all_gather([acc], out_numpy=True, is_valid=idx < loader.sampler.valid_length)
                avg_acc = np.mean([np.nanmean(l) for l in acc_list])

            else:
                acc_list = acc.detach().cpu().numpy()
                avg_acc = np.mean([np.nanmean(l) for l in acc_list])
                print(acc_list)
                # input()
            #print(img_name, acc_list[0] * 100)
            if args.rank == 0:
                print(
                    "Val {}/{} {}/{}".format(epoch, args.max_epochs, idx, len(loader)),
                    "acc",
                    avg_acc,
                    "time {:.2f}s".format(time.time() - start_time),
                )
            val_dice.extend(acc_list)
            
            del logits, val_output_convert, val_labels_convert, acc
            torch.cuda.empty_cache()
            
            start_time = time.time()
    if args.distributed:
        flat = [arr for sublist in val_dice for arr in sublist]
        val_dice = np.vstack(flat)
    print(val_dice)
    return np.nanmean(val_dice, axis=0)


def save_checkpoint(model, epoch, args, filename="model.pt", best_acc=0, optimizer=None, scheduler=None):
    state_dict = model.state_dict() if not args.distributed else model.module.state_dict()
    save_dict = {"epoch": epoch, "best_acc": best_acc, "state_dict": state_dict}
    if optimizer is not None:
        save_dict["optimizer"] = optimizer.state_dict()
    if scheduler is not None:
        save_dict["scheduler"] = scheduler.state_dict()
    filename = os.path.join(args.logdir, filename)
    torch.save(save_dict, filename)
    print("Saving checkpoint", filename)


def run_training_semi(
    model,
    model2,
    train_loader_lab,
    train_loader_unlab,
    val_loader,
    optimizer,
    optimizer2,
    loss_func,
    acc_func,
    args,
    model_inferer=None,
    model_inferer2=None,
    scheduler=None,
    scheduler2=None,
    start_epoch=0,
    post_label=None,
    post_pred=None,
):
    writer = None
    if args.logdir is not None and args.rank == 0:
        writer = SummaryWriter(log_dir=args.logdir)
        if args.rank == 0:
            print("Writing Tensorboard logs to ", args.logdir)
    scaler = None
    if args.amp:
        scaler = GradScaler()
    val_acc_max_model1 = 0.0
    val_acc_max_model2 = 0.0
    for epoch in range(start_epoch, args.max_epochs):
        if args.distributed:
            train_loader_lab.sampler.set_epoch(epoch)
            train_loader_unlab.sampler.set_epoch(epoch)
            torch.distributed.barrier()
        #print(args.rank, time.ctime(), "Epoch:", epoch)
        epoch_time = time.time()
        train_loss = train_epoch(
            model, model2, train_loader_lab, train_loader_unlab, optimizer, optimizer2, scaler=scaler, epoch=epoch, loss_func=loss_func, args=args
        )
        if args.rank == 0:
            print(
                "Final training  {}/{}".format(epoch, args.max_epochs - 1),
                "loss: {:.4f}".format(train_loss),
                "time {:.2f}s".format(time.time() - epoch_time),
            )
        if args.rank == 0 and writer is not None:
            writer.add_scalar("train_loss", train_loss, epoch)
        b_new_best = False
        if (epoch + 1) % args.val_every == 0:
            print('----------------------- model1 validation -------------------------')
            if args.distributed:
                torch.distributed.barrier()
            epoch_time = time.time()
            val_avg_acc_classes = val_epoch(
                model,
                val_loader,
                epoch=epoch,
                acc_func=acc_func,
                model_inferer=model_inferer,
                args=args,
                post_label=post_label,
                post_pred=post_pred,
            )
            print(val_avg_acc_classes)
            print(np.nanmean(val_avg_acc_classes))
            val_avg_acc = np.nanmean(val_avg_acc_classes)

            torch.cuda.empty_cache()
            gc.collect()
            if args.rank == 0:
                print(
                    "Final validation  {}/{}".format(epoch, args.max_epochs - 1),
                    "acc",
                    val_avg_acc,
                    "time {:.2f}s".format(time.time() - epoch_time),
                )
                if writer is not None:
                    writer.add_scalar("model1_val_dice", val_avg_acc, epoch)
                    writer.add_scalar("model1_val_dice_liver", val_avg_acc_classes[0], epoch)
                    writer.add_scalar("model1_val_dice_tumor", val_avg_acc_classes[1], epoch)
                if val_avg_acc > val_acc_max_model1:
                    print("new best ({:.6f} --> {:.6f}). ".format(val_acc_max_model1, val_avg_acc))
                    val_acc_max_model1 = val_avg_acc
                    b_new_best = True
                    if args.rank == 0 and args.logdir is not None:
                        save_checkpoint(
                            model, epoch, args, best_acc=val_acc_max_model1, optimizer=optimizer, scheduler=scheduler, filename="model1.pt"
                        )
            if args.rank == 0 and args.logdir is not None:
                save_checkpoint(model, epoch, args, best_acc=val_acc_max_model1, filename="model1_final.pt")

            # ----------------------------------------------------
            print('----------------------- model2 validation -------------------------')
            if args.distributed:
                torch.distributed.barrier()
            epoch_time = time.time()
            # 注意这里model_inferer里指定的model才是真正推理时用到的model
            val_avg_acc_classes = val_epoch(
                model2,
                val_loader,
                epoch=epoch,
                acc_func=acc_func,
                model_inferer=model_inferer2,
                args=args,
                post_label=post_label,
                post_pred=post_pred,
            )
            print(val_avg_acc_classes)
            print(np.nanmean(val_avg_acc_classes))
            val_avg_acc = np.nanmean(val_avg_acc_classes)

            torch.cuda.empty_cache()
            gc.collect()
            if args.rank == 0:
                print(
                    "Final validation  {}/{}".format(epoch, args.max_epochs - 1),
                    "acc",
                    val_avg_acc,
                    "time {:.2f}s".format(time.time() - epoch_time),
                )
                if writer is not None:
                    writer.add_scalar("model2_val_dice", val_avg_acc, epoch)
                    writer.add_scalar("model2_val_dice_liver", val_avg_acc_classes[0], epoch)
                    writer.add_scalar("model2_val_dice_tumor", val_avg_acc_classes[1], epoch)
                if val_avg_acc > val_acc_max_model2:
                    print("new best ({:.6f} --> {:.6f}). ".format(val_acc_max_model2, val_avg_acc))
                    val_acc_max_model2 = val_avg_acc
                    b_new_best = True
                    if args.rank == 0 and args.logdir is not None:
                        save_checkpoint(
                            model2, epoch, args, best_acc=val_acc_max_model2, optimizer=optimizer, scheduler=scheduler2, filename="model2.pt"
                        )
            if args.rank == 0 and args.logdir is not None:
                save_checkpoint(model2, epoch, args, best_acc=val_acc_max_model2, filename="model2_final.pt")
                

        if scheduler is not None:
            scheduler.step()
        if scheduler2 is not None:
            scheduler2.step()

    print("Training Finished !, Best Accuracy: ", max(val_acc_max_model1, val_acc_max_model2))

    return max(val_acc_max_model1, val_acc_max_model2)
