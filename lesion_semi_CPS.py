# Copyright 2020 - 2021 MONAI Consortium
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import os
from functools import partial
import logging
logging.disable(logging.WARNING)
import numpy as np
import torch

cpu_num = 1
os.environ['OMP_NUM_THREADS'] = str(cpu_num)
os.environ['OPENBLAS_NUM_THREADS'] = str(cpu_num)
os.environ['MKL_NUM_THREADS'] = str(cpu_num)
os.environ['VECLIB_MAXIMUM_THREADS'] = str(cpu_num)
os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_num)
torch.set_num_threads(cpu_num)
torch.multiprocessing.set_sharing_strategy('file_system')
import torch.distributed as dist
import torch.multiprocessing as mp
import torch.nn.parallel
import torch.utils.data.distributed
from optimizers.lr_scheduler import LinearWarmupCosineAnnealingLR
from trainer_semi_CPS import run_training_semi
from utils.cache_utils_lesion import get_loader_semi
from monai.inferers import sliding_window_inference
from monai.losses import DiceCELoss, DiceLoss
from monai.metrics import DiceMetric
from monai.transforms import Activations, AsDiscrete, Compose
from monai.utils.enums import MetricReduction
from networks.VNet import *
from factory import load_mae_p12_10k

from dhc import kaiming_normal_init_weight, xavier_normal_init_weight

def get_args(args):
    if args.data == "kits23_bs4":
        args.data_dir = "/data1/fenghetang/lesion/Dataset023_KiTS2023/"
        args.out_channels = 4
        args.json_list = "dataset.json"
        args.max_epochs = 1000
        args.val_every = 50   
        args.batch_size = 4
        args.num_samples = 2    
        args.space_z = 2.0
    elif args.data == "msd_liver_bs4": # best
        args.data_dir = "../data/Task03_Liver/"
        args.out_channels = 3
        args.json_list = "json/lab_15.json"
        # args.data_unlab_dir = "../data/Dataset901_privateLiver/"
        # args.json_list_unlab = "json/unlab.json"
        args.data_unlab_dir = "../data/Task03_Liver/"
        args.json_list_unlab = "json/unlab_msd_liver_test.json"
        args.max_epochs = 1000
        args.val_every = 50
        args.batch_size = 4
        args.num_samples = 2
    elif args.data == "pancreas_bs4": # best
        args.data_dir = "../data/tumor/Dataset016_Pancreas"
        args.out_channels = 3
        args.json_list = "json/pancreas/pancreas_tumor_lab35.json"
        args.data_unlab_dir = "../data/tumor/Dataset016_Pancreas"
        args.json_list_unlab = "json/pancreas/pancreas_tumor_unlab.json"
        args.max_epochs = 1000
        args.val_every = 50
        args.batch_size = 4
        args.num_samples = 2
        args.a_min = -87
        args.a_max = 199
    
    return args
    
        
parser = argparse.ArgumentParser(description="Universal segmentation pipeline")
parser.add_argument("--model_name", default='load_vnet', type=str, help="model name")
parser.add_argument("--num_samples", type=int, default=4)
parser.add_argument("--checkpoint", default=None, help="start training from saved checkpoint")
parser.add_argument(
    "--name",
    default="full",
    choices=["full", "r1", "r10"],
)
parser.add_argument("--logdir", default="", type=str, help="directory to save the tensorboard logs")
parser.add_argument(
    "--pretrained_dir", default="./pretrain_models", type=str, help="pretrained checkpoint directory"
)
parser.add_argument("--data", default="pancreas_bs4", help="dataset")
parser.add_argument("--data_dir", default="/data2/fenghetang/data/Dataset009_AMOS/", type=str, help="dataset directory")
parser.add_argument("--json_list", default="dataset_10.json", type=str, help="dataset json file")
parser.add_argument(
    "--pretrained_model_name", default="model_final.pt", type=str, help="pretrained model name"
)
parser.add_argument("--save_checkpoint", action="store_true", help="save checkpoint during training")
parser.add_argument("--max_epochs", default=30, type=int, help="max number of training epochs")
parser.add_argument("--batch_size", default=4, type=int, help="number of batch size")
parser.add_argument("--sw_batch_size", default=1, type=int, help="number of sliding window batch size")
parser.add_argument("--optim_lr", default=1e-4, type=float, help="optimization learning rate")
parser.add_argument("--optim_name", default="adamw", type=str, help="optimization algorithm")
parser.add_argument("--reg_weight", default=1e-5, type=float, help="regularization weight")
parser.add_argument("--momentum", default=0.99, type=float, help="momentum")
parser.add_argument("--noamp", action="store_true", help="do NOT use amp for training")
parser.add_argument("--val_every", default=6, type=int, help="validation frequency") # 10
parser.add_argument("--distributed", action="store_true", help="start distributed training")
parser.add_argument("--world_size", default=1, type=int, help="number of nodes for distributed training")
parser.add_argument("--rank", default=0, type=int, help="node rank for distributed training")
parser.add_argument("--dist-url", default="tcp://127.0.0.1:11111", type=str, help="distributed url")
parser.add_argument("--dist-backend", default="nccl", type=str, help="distributed backend")
parser.add_argument("--workers", default=8, type=int, help="number of workers")
parser.add_argument("--pos_embed", default="conv", type=str, help="type of position embedding")
parser.add_argument("--norm_name", default="instance", type=str, help="normalization layer type in decoder")
parser.add_argument("--num_heads", default=16, type=int, help="number of attention heads in ViT encoder")
parser.add_argument("--mlp_dim", default=1536 * 4, type=int, help="mlp dimention in ViT encoder")
parser.add_argument("--hidden_size", default=1536, type=int, help="hidden size dimention in ViT encoder")
parser.add_argument("--feature_size", default=32, type=int, help="feature size dimention")
parser.add_argument("--in_channels", default=1, type=int, help="number of input channels")
parser.add_argument("--out_channels", default=3, type=int, help="number of output channels")
parser.add_argument("--res_block", action="store_true", help="use residual blocks")
parser.add_argument("--conv_block", action="store_true", help="use conv blocks")
parser.add_argument("--use_normal_dataset", action="store_true", help="use monai Dataset class")
parser.add_argument("--a_min", default=-175.0, type=float, help="a_min in ScaleIntensityRanged")
parser.add_argument("--a_max", default=250.0, type=float, help="a_max in ScaleIntensityRanged")
parser.add_argument("--b_min", default=0.0, type=float, help="b_min in ScaleIntensityRanged")
parser.add_argument("--b_max", default=1.0, type=float, help="b_max in ScaleIntensityRanged")
parser.add_argument("--space_x", default=1.5, type=float, help="spacing in x direction")
parser.add_argument("--space_y", default=1.5, type=float, help="spacing in y direction")
parser.add_argument("--space_z", default=1.5, type=float, help="spacing in z direction")
parser.add_argument("--roi_x", default=96, type=int, help="roi size in x direction")
parser.add_argument("--roi_y", default=96, type=int, help="roi size in y direction")
parser.add_argument("--roi_z", default=96, type=int, help="roi size in z direction")
parser.add_argument("--dropout_rate", default=0.0, type=float, help="dropout rate")
parser.add_argument("--RandFlipd_prob", default=0.2, type=float, help="RandFlipd aug probability")
parser.add_argument("--RandRotate90d_prob", default=0.2, type=float, help="RandRotate90d aug probability")
parser.add_argument("--RandScaleIntensityd_prob", default=0.1, type=float, help="RandScaleIntensityd aug probability")
parser.add_argument("--RandShiftIntensityd_prob", default=0.1, type=float, help="RandShiftIntensityd aug probability")
parser.add_argument("--infer_overlap", default=0.5, type=float, help="sliding window inference overlap")
parser.add_argument("--lrschedule", default="warmup_cosine", type=str, help="type of learning rate scheduler")
parser.add_argument("--warmup_epochs", default=10, type=int, help="number of warmup epochs")
parser.add_argument("--resume_ckpt", action="store_true", help="resume training from pretrained checkpoint")
parser.add_argument("--resume_jit", action="store_true", help="resume training from pretrained torchscript checkpoint")
parser.add_argument("--smooth_dr", default=1e-6, type=float, help="constant added to dice denominator to avoid nan")
parser.add_argument("--smooth_nr", default=0.0, type=float, help="constant added to dice numerator to avoid zero")
parser.add_argument("--use_ckpt", action="store_true", help="use gradient checkpointing to save memory")

def main():
    args = get_args(parser.parse_args())
    args.logdir = "{}_{}_{}semi_CPS".format(args.model_name, args.name, args.data_dir.split("/")[-2])
    args.amp = not args.noamp
    print("use amp: {}".format(args.amp))
    #print(args.json_list)
    args.logdir = f"./{args.data}/" + args.logdir
    #args.logdir = "/root/tf-logs"
    if args.distributed:
        args.ngpus_per_node = torch.cuda.device_count()
        print("Found total gpus", args.ngpus_per_node)
        args.world_size = args.ngpus_per_node * args.world_size
        mp.spawn(main_worker, nprocs=args.ngpus_per_node, args=(args,))
    else:
        main_worker(gpu=0, args=args)


def main_worker(gpu, args):
    if args.distributed:
        torch.multiprocessing.set_start_method("fork", force=True)
    np.set_printoptions(formatter={"float": "{: 0.3f}".format}, suppress=True)
    args.gpu = gpu
    if args.distributed:
        args.rank = args.rank * args.ngpus_per_node + gpu
        dist.init_process_group(
            backend=args.dist_backend, init_method=args.dist_url, world_size=args.world_size, rank=args.rank
        )
    torch.cuda.set_device(args.gpu)
    torch.backends.cudnn.benchmark = True
    args.test_mode = False
    
    loader = get_loader_semi(args)
    print(args.rank, " gpu", args.gpu)
    if args.rank == 0:
        print("Batch size is:", args.batch_size, "epochs", args.max_epochs)
    inf_size = [args.roi_x, args.roi_y, args.roi_z]
    pretrained_dir = args.pretrained_dir
        
    model = VNet(n_channels=1, n_classes=args.out_channels, normalization='batchnorm', has_dropout=True).cuda()
    # model2 = load_mae_p12_10k(args.out_channels)
    model2 = VNet(n_channels=1, n_classes=args.out_channels, normalization='batchnorm', has_dropout=True).cuda()

    model = kaiming_normal_init_weight(model)
    model2 = kaiming_normal_init_weight(model2)

    # if True:
    #     ckpt = "./msd_liver_bs4/vnet_full"
    #     model_dict = torch.load(os.path.join(ckpt, "model.pt"))["state_dict"]
    #     model.load_state_dict(model_dict, strict=True)
    #     print(f"Load {ckpt}")

    #     ckpt = "./msd_liver_bs4/mae_full"
    #     model_dict = torch.load(os.path.join(ckpt, "model.pt"))["state_dict"]
    #     model2.load_state_dict(model_dict, strict=True)
    #     print(f"Load {ckpt}")

    if args.rank == 0:
        print(model)
        print(model2)
        print(f"{args.model_name} data: ", args.data)
        print(f"{args.model_name} data_dir: ", args.data_dir)
        print(f"{args.model_name} max_epochs: ", args.max_epochs)
        print(f"{args.model_name} val_every: ", args.val_every)
        print(f"{args.model_name} json_list: ", args.json_list)
        print(f"{args.batch_size} batch_size: ", args.batch_size)
        print(f"{args.num_samples} num_samples: ", args.num_samples)
      
    

    if args.resume_jit:
        if not args.noamp:
            print("Training from pre-trained checkpoint does not support AMP\nAMP is disabled.")
            args.amp = args.noamp
        model = torch.jit.load(os.path.join(pretrained_dir, args.pretrained_model_name))


    dice_loss = DiceCELoss(
        to_onehot_y=True, softmax=True, squared_pred=True, smooth_nr=args.smooth_nr, smooth_dr=args.smooth_dr
    )


    post_label = AsDiscrete(to_onehot=args.out_channels, n_classes=args.out_channels)
    post_pred = AsDiscrete(argmax=True, to_onehot=args.out_channels, n_classes=args.out_channels)
    dice_acc = DiceMetric(include_background=False, reduction=MetricReduction.MEAN, get_not_nans=True)
    model_inferer = partial(
        sliding_window_inference,
        roi_size=inf_size,
        sw_batch_size=args.sw_batch_size,
        predictor=model,
        overlap=args.infer_overlap,
    )
    model_inferer2 = partial(
        sliding_window_inference,
        roi_size=inf_size,
        sw_batch_size=args.sw_batch_size,
        predictor=model2,
        overlap=args.infer_overlap,
    )

    pytorch_total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print("Total parameters count", pytorch_total_params)
    pytorch_total_params = sum(p.numel() for p in model2.parameters() if p.requires_grad)
    print("Total parameters count", pytorch_total_params)

    best_acc = 0
    start_epoch = 0

    if args.checkpoint is not None:
        checkpoint = torch.load(args.checkpoint, map_location="cpu")
        from collections import OrderedDict

        new_state_dict = OrderedDict()
        for k, v in checkpoint["state_dict"].items():
            new_state_dict[k.replace("backbone.", "")] = v
        model.load_state_dict(new_state_dict, strict=False)
        if "epoch" in checkpoint:
            start_epoch = checkpoint["epoch"]
        if "best_acc" in checkpoint:
            best_acc = checkpoint["best_acc"]
        print("=> loaded checkpoint '{}' (epoch {}) (bestacc {})".format(args.checkpoint, start_epoch, best_acc))

    model.cuda(args.gpu)
    model2.cuda(args.gpu)
    
    
    if args.distributed:
        torch.cuda.set_device(args.gpu)
        if args.norm_name == "batch":
            model = torch.nn.SyncBatchNorm.convert_sync_batchnorm(model)
        model.cuda(args.gpu)
        model2.cuda(args.gpu)
        model = torch.nn.parallel.DistributedDataParallel(
            model, device_ids=[args.gpu], output_device=args.gpu, find_unused_parameters=True
        )
        model2 = torch.nn.parallel.DistributedDataParallel(
            model2, device_ids=[args.gpu], output_device=args.gpu, find_unused_parameters=True
        )
    if args.optim_name == "adam":
        optimizer = torch.optim.Adam(params=[
                {'params': model.encoder.parameters(), 'lr': 0.1 * args.optim_lr},
                {'params': model.decoder.parameters(), 'lr': args.optim_lr},
            ], lr=args.optim_lr, weight_decay=args.reg_weight)
        optimizer2 = torch.optim.Adam(params=[
                {'params': model2.encoder.parameters(), 'lr': 0.1 * args.optim_lr},
                {'params': model2.decoder.parameters(), 'lr': args.optim_lr},
            ], lr=args.optim_lr, weight_decay=args.reg_weight)
    elif args.optim_name == "adamw":
    #    optimizer = torch.optim.AdamW(params=[
    #    {'params': model.encoder.parameters(), 'lr': 0.5 * args.optim_lr},
    #    {'params': model.decoder.parameters(), 'lr': args.optim_lr},
    #], lr=args.optim_lr, weight_decay=args.reg_weight)
    
        optimizer = torch.optim.AdamW(model.parameters(), lr=args.optim_lr, weight_decay=args.reg_weight)
        optimizer2 = torch.optim.AdamW(model2.parameters(), lr=args.optim_lr, weight_decay=args.reg_weight)
    
    elif args.optim_name == "sgd":
        optimizer = torch.optim.SGD(
                    params=[
                {'params': model.encoder.parameters(), 'lr': 0.1 * args.optim_lr},
                {'params': model.decoder.parameters(), 'lr': args.optim_lr},
            ], lr=args.optim_lr, momentum=args.momentum, nesterov=True, weight_decay=args.reg_weight
                )
        optimizer2 = torch.optim.SGD(
                    params=[
                {'params': model2.encoder.parameters(), 'lr': 0.1 * args.optim_lr},
                {'params': model2.decoder.parameters(), 'lr': args.optim_lr},
            ], lr=args.optim_lr, momentum=args.momentum, nesterov=True, weight_decay=args.reg_weight
                )
    else:
        raise ValueError("Unsupported Optimization Procedure: " + str(args.optim_name))

    if args.lrschedule == "warmup_cosine":
        scheduler = LinearWarmupCosineAnnealingLR(
            optimizer, warmup_epochs=args.warmup_epochs, max_epochs=args.max_epochs
        )
        scheduler2 = LinearWarmupCosineAnnealingLR(
            optimizer2, warmup_epochs=args.warmup_epochs, max_epochs=args.max_epochs
        )
    elif args.lrschedule == "cosine_anneal":
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.max_epochs)
        if args.checkpoint is not None:
            scheduler.step(epoch=start_epoch)
        scheduler2 = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer2, T_max=args.max_epochs)
        if args.checkpoint is not None:
            scheduler2.step(epoch=start_epoch)
    else:
        scheduler = None
        scheduler2 = None
    args.amp = True
    accuracy = run_training_semi(
        model=model,
        model2=model2,
        train_loader_lab=loader[0],
        train_loader_unlab=loader[1],
        val_loader=loader[2],
        optimizer=optimizer,
        optimizer2=optimizer2,
        loss_func=dice_loss,
        acc_func=dice_acc,
        args=args,
        model_inferer=model_inferer,
        model_inferer2=model_inferer2,
        scheduler=scheduler,
        scheduler2=scheduler2,
        start_epoch=start_epoch,
        post_label=post_label,
        post_pred=post_pred,
    )
    
    #inference(args, model, loader[1])
    
    return accuracy


if __name__ == "__main__":
    main()
