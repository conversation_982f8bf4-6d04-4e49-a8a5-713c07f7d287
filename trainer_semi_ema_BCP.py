# Copyright 2020 - 2021 MONAI Consortium
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import shutil
import time
import gc
import numpy as np
import torch
import torch.nn.parallel
import torch.utils.data.distributed
from tensorboardX import SummaryWriter
from torch.cuda.amp import GradScaler, autocast
from utils.utils import distributed_all_gather

from monai.data import decollate_batch

import torch.nn as nn


def get_probability(logits):
    """ Get probability from logits, if the channel of logits is 1 then use sigmoid else use softmax.
    :param logits: [N, C, H, W] or [N, C, D, H, W]
    :return: prediction and class num
    """
    size = logits.size()
    # N x 1 x H x W
    if size[1] > 1:
        pred = F.softmax(logits, dim=1)
        nclass = size[1]
    else:
        pred = F.sigmoid(logits)
        pred = torch.cat([1 - pred, pred], 1)
        nclass = 2
    return pred, nclass

def to_one_hot(tensor, nClasses):
    """ Input tensor : Nx1xHxW
    :param tensor:
    :param nClasses:
    :return:
    """
    assert tensor.max().item() < nClasses, 'one hot tensor.max() = {} < {}'.format(torch.max(tensor), nClasses)
    assert tensor.min().item() >= 0, 'one hot tensor.min() = {} < {}'.format(tensor.min(), 0)

    size = list(tensor.size())
    assert size[1] == 1
    size[1] = nClasses
    one_hot = torch.zeros(*size)
    if tensor.is_cuda:
        one_hot = one_hot.cuda(tensor.device)
    one_hot = one_hot.scatter_(1, tensor, 1)
    return one_hot

class mask_DiceLoss(nn.Module):
    def __init__(self, nclass, class_weights=None, smooth=1e-5):
        super(mask_DiceLoss, self).__init__()
        self.smooth = smooth
        if class_weights is None:
            # default weight is all 1
            self.class_weights = nn.Parameter(torch.ones((1, nclass)).type(torch.float32), requires_grad=False)
        else:
            class_weights = np.array(class_weights)
            assert nclass == class_weights.shape[0]
            self.class_weights = nn.Parameter(torch.tensor(class_weights, dtype=torch.float32), requires_grad=False)

    def prob_forward(self, pred, target, mask=None):
        size = pred.size()
        N, nclass = size[0], size[1]
        # N x C x H x W
        pred_one_hot = pred.view(N, nclass, -1)
        target = target.view(N, 1, -1)
        target_one_hot = to_one_hot(target.type(torch.long), nclass).type(torch.float32)

        # N x C x H x W
        inter = pred_one_hot * target_one_hot
        union = pred_one_hot + target_one_hot

        if mask is not None:
            mask = mask.view(N, 1, -1)
            inter = (inter.view(N, nclass, -1) * mask).sum(2)
            union = (union.view(N, nclass, -1) * mask).sum(2)
        else:
            # N x C
            inter = inter.view(N, nclass, -1).sum(2)
            union = union.view(N, nclass, -1).sum(2)

        # smooth to prevent overfitting
        # [https://github.com/pytorch/pytorch/issues/1249]
        # NxC
        dice = (2 * inter + self.smooth) / (union + self.smooth)
        return 1 - dice.mean()

    def forward(self, logits, target, mask=None):
        size = logits.size()
        N, nclass = size[0], size[1]

        logits = logits.view(N, nclass, -1)
        target = target.view(N, 1, -1)

        pred, nclass = get_probability(logits)

        # N x C x H x W
        pred_one_hot = pred
        target_one_hot = to_one_hot(target.type(torch.long), nclass).type(torch.float32)

        # N x C x H x W
        inter = pred_one_hot * target_one_hot
        union = pred_one_hot + target_one_hot

        if mask is not None:
            mask = mask.view(N, 1, -1)
            inter = (inter.view(N, nclass, -1) * mask).sum(2)
            union = (union.view(N, nclass, -1) * mask).sum(2)
        else:
            # N x C
            inter = inter.view(N, nclass, -1).sum(2)
            union = union.view(N, nclass, -1).sum(2)

        # smooth to prevent overfitting
        # [https://github.com/pytorch/pytorch/issues/1249]
        # NxC
        dice = (2 * inter + self.smooth) / (union + self.smooth)
        return 1 - dice.mean()


DICE = mask_DiceLoss(nclass=3)
CE = nn.CrossEntropyLoss(reduction='none')
def dice(x, y):
    intersect = np.sum(np.sum(np.sum(x * y)))
    y_sum = np.sum(np.sum(np.sum(y)))
    if y_sum == 0:
        return 0.0
    x_sum = np.sum(np.sum(np.sum(x)))
    return 2 * intersect / (x_sum + y_sum)


class AverageMeter(object):
    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = np.where(self.count > 0, self.sum / self.count, self.sum)

def sigmoid_rampup(current, rampup_length):
    """Exponential rampup from https://arxiv.org/abs/1610.02242"""
    if rampup_length == 0:
        return 1.0
    else:               
        current = np.clip(current, 0.0, rampup_length)
        phase = 1.0 - current / rampup_length
        return float(np.exp(-5.0 * phase * phase))

def get_current_consistency_weight(epoch):
    # Consistency ramp-up from https://arxiv.org/abs/1610.02242
    return 0.1 * sigmoid_rampup(epoch, 0)

def update_ema_variables(model, ema_model, alpha, global_step):
    # Use the true average until the exponential average is more correct
    alpha = min(1 - 1 / (global_step + 1), alpha)
    for ema_param, param in zip(ema_model.parameters(), model.parameters()):
        ema_param.data.mul_(alpha).add_(1 - alpha, param.data)

import torch.nn.functional as F
from skimage.measure import label
def get_cut_mask(out, thres=0.5, nms=0):
    probs = F.softmax(out, 1)
    masks = (probs >= thres).type(torch.int64)
    masks = masks[:, 1, :, :].contiguous()
    if nms == 1:
        masks = LargestCC_pancreas(masks)
    return masks

def LargestCC_pancreas(segmentation):
    N = segmentation.shape[0]
    batch_list = []
    for n in range(N):
        n_prob = segmentation[n].detach().cpu().numpy()
        labels = label(n_prob)
        if labels.max() != 0:
            largestCC = labels == np.argmax(np.bincount(labels.flat)[1:])+1
        else:
            largestCC = n_prob
        batch_list.append(largestCC)
    
    return torch.Tensor(batch_list).cuda()

def context_mask(img, mask_ratio, patch_size):
    batch_size, channel, img_x, img_y, img_z = img.shape[0],img.shape[1],img.shape[2],img.shape[3],img.shape[4]
    loss_mask = torch.ones(batch_size, img_x, img_y, img_z).cuda()
    mask = torch.ones(img_x, img_y, img_z).cuda()
    patch_pixel_x, patch_pixel_y, patch_pixel_z = int(img_x*mask_ratio), int(img_y*mask_ratio), int(img_z*mask_ratio)
    w = np.random.randint(0, patch_size[0] - patch_pixel_x)
    h = np.random.randint(0, patch_size[1] - patch_pixel_y)
    z = np.random.randint(0, patch_size[2] - patch_pixel_z)
    mask[w:w+patch_pixel_x, h:h+patch_pixel_y, z:z+patch_pixel_z] = 0
    loss_mask[:, w:w+patch_pixel_x, h:h+patch_pixel_y, z:z+patch_pixel_z] = 0
    return mask.long(), loss_mask.long()

def mix_loss(net3_output, img_l, patch_l, mask, l_weight=1.0, u_weight=0.5, unlab=False):
    #  l_weight: 有标签数据权重；u_weight: 无标签数据权重。
    img_l, patch_l = img_l.type(torch.int64), patch_l.type(torch.int64)
    image_weight, patch_weight = l_weight, u_weight
    if unlab:
        image_weight, patch_weight = u_weight, l_weight
    patch_mask = 1 - mask
    dice_loss = DICE(net3_output, img_l, mask) * image_weight 
    dice_loss += DICE(net3_output, patch_l, patch_mask) * patch_weight
    loss_ce = image_weight * (CE(net3_output, img_l) * mask).sum() / (mask.sum() + 1e-16)   # + 1e-16 是为了避免被0除
    loss_ce += patch_weight * (CE(net3_output, patch_l) * patch_mask).sum() / (patch_mask.sum() + 1e-16)
    loss = (dice_loss + loss_ce) / 2
    return loss

def train_epoch(model, model2, loader_lab, loader_unlab, optimizer, optimizer2, scaler, epoch, loss_func, args):
    model.train()
    model2.train()
    start_time = time.time()
    run_loss = AverageMeter()
    
    consistency_weight = get_current_consistency_weight(epoch)

    sub_bs = int((args.batch_size * args.num_samples)/2)
    
    for idx, (batch_data_lab, batch_data_unlab) in enumerate(zip(loader_lab, loader_unlab)):
        data_lab, target_lab = batch_data_lab["image"], batch_data_lab["label"]
        target_lab = target_lab.squeeze(1)
        data_lab, target_lab = data_lab.cuda(args.rank), target_lab.cuda(args.rank)

        data_unlab = batch_data_unlab["image"]
        data_unlab = data_unlab.cuda(args.rank)

        img_a, img_b = data_lab[:sub_bs], data_lab[sub_bs:]
        lab_a, lab_b = target_lab[:sub_bs], target_lab[sub_bs:]
        unimg_a, unimg_b = data_unlab[:sub_bs], data_unlab[sub_bs:]


        for param in model.parameters():
            param.grad = None
        with autocast(enabled=args.amp):
            with torch.no_grad():
                unoutput_a = model2(unimg_a)
                unoutput_b = model2(unimg_b)
                plab_a = get_cut_mask(unoutput_a, nms=0)  # pseudo label a, nms = 1表示执行最大连通域提取
                plab_b = get_cut_mask(unoutput_b, nms=0)
                img_mask, loss_mask = context_mask(img_a, 2/3, (96, 96, 96))

            mixl_img = img_a * img_mask + unimg_a * (1 - img_mask)  # label在外，unlabel在里，即label是主导
            mixu_img = unimg_b * img_mask + img_b * (1 - img_mask)
            mixl_lab = lab_a * img_mask + plab_a * (1 - img_mask)
            mixu_lab = plab_b * img_mask + lab_b * (1 - img_mask)

            outputs_l = model(mixl_img)
            outputs_u = model(mixu_img)
            
            loss_l = mix_loss(outputs_l, lab_a, plab_a, loss_mask, u_weight=0.5)
            loss_u = mix_loss(outputs_u, plab_b, lab_b, loss_mask, u_weight=0.5, unlab=True)

            loss = loss_l + loss_u
            print("loss: {:.4f}, loss_l: {:.4f}, loss_u: {:.4f}".format(loss.item(), loss_l.item(), loss_u.item()))
        
        
        if args.amp:
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            loss.backward()
            optimizer.step()
        if args.distributed:
            loss_list = distributed_all_gather([loss], out_numpy=True, is_valid=idx < loader_lab.sampler.valid_length)
            run_loss.update(
                np.mean(np.mean(np.stack(loss_list, axis=0), axis=0), axis=0), n=args.batch_size * args.world_size
            )
        else:
            run_loss.update(loss.item(), n=args.batch_size)

        update_ema_variables(model, model2, 0.99, epoch)
        
        
    if args.rank == 0:
        print(
            "Epoch {}/{}".format(epoch, args.max_epochs),
            "loss: {:.4f}".format(run_loss.avg),
            "time {:.2f}s".format(time.time() - start_time),
        )
    start_time = time.time()
    for param in model.parameters():
        param.grad = None
    return run_loss.avg


def val_epoch(model, loader, epoch, acc_func, args, model_inferer=None, post_label=None, post_pred=None):
    model.eval()
    start_time = time.time()
    val_dice = []
    with torch.no_grad():
        for idx, batch_data in enumerate(loader):
            if isinstance(batch_data, list):
                data, target = batch_data
            else:
                data, target = batch_data["image"], batch_data["label"]
            data, target = data.cuda(args.rank), target.cuda(args.rank)
            img_name = batch_data["image_meta_dict"]["filename_or_obj"][0].split("/")[-1]
            with autocast(enabled=args.amp):
                if model_inferer is not None:
                    logits = model_inferer(data)
                else:
                    logits = model(data)
            if not logits.is_cuda:
                target = target.cpu()
            val_labels_list = decollate_batch(target)
            val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]
            val_outputs_list = decollate_batch(logits)
            val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]
            acc = acc_func(y_pred=val_output_convert, y=val_labels_convert)
            acc = acc.cuda(args.rank)

            if args.distributed:
                acc_list = distributed_all_gather([acc], out_numpy=True, is_valid=idx < loader.sampler.valid_length)
                avg_acc = np.mean([np.nanmean(l) for l in acc_list])

            else:
                acc_list = acc.detach().cpu().numpy()
                avg_acc = np.mean([np.nanmean(l) for l in acc_list])
                print(acc_list)
                # input()
            #print(img_name, acc_list[0] * 100)
            if args.rank == 0:
                print(
                    "Val {}/{} {}/{}".format(epoch, args.max_epochs, idx, len(loader)),
                    "acc",
                    avg_acc,
                    "time {:.2f}s".format(time.time() - start_time),
                )
            val_dice.extend(acc_list)
            
            del logits, val_output_convert, val_labels_convert, acc
            torch.cuda.empty_cache()
            
            start_time = time.time()
    if args.distributed:
        flat = [arr for sublist in val_dice for arr in sublist]
        val_dice = np.vstack(flat)
    print(val_dice)
    return np.nanmean(val_dice, axis=0)


def save_checkpoint(model, epoch, args, filename="model.pt", best_acc=0, optimizer=None, optimizer2=None, scheduler=None):
    state_dict = model.state_dict() if not args.distributed else model.module.state_dict()
    save_dict = {"epoch": epoch, "best_acc": best_acc, "state_dict": state_dict}
    if optimizer is not None:
        save_dict["optimizer"] = optimizer.state_dict()
    if optimizer2 is not None:
        save_dict["optimizer2"] = optimizer2.state_dict()
    if scheduler is not None:
        save_dict["scheduler"] = scheduler.state_dict()
    filename = os.path.join(args.logdir, filename)
    torch.save(save_dict, filename)
    print("Saving checkpoint", filename)


def run_training_semi(
    model,
    model2,
    train_loader_lab,
    train_loader_unlab,
    val_loader,
    optimizer,
    optimizer2,
    loss_func,
    acc_func,
    args,
    model_inferer=None,
    model_inferer2=None,
    scheduler=None,
    start_epoch=0,
    post_label=None,
    post_pred=None,
):
    writer = None
    if args.logdir is not None and args.rank == 0:
        writer = SummaryWriter(log_dir=args.logdir)
        if args.rank == 0:
            print("Writing Tensorboard logs to ", args.logdir)
    scaler = None
    if args.amp:
        scaler = GradScaler()
    val_acc_max_model1 = 0.0
    val_acc_max_model2 = 0.0
    for epoch in range(start_epoch, args.max_epochs):
        if args.distributed:
            train_loader_lab.sampler.set_epoch(epoch)
            train_loader_unlab.sampler.set_epoch(epoch)
            torch.distributed.barrier()
        #print(args.rank, time.ctime(), "Epoch:", epoch)
        epoch_time = time.time()
        train_loss = train_epoch(
            model, model2, train_loader_lab, train_loader_unlab, optimizer, optimizer2, scaler=scaler, epoch=epoch, loss_func=loss_func, args=args
        )
        if args.rank == 0:
            print(
                "Final training  {}/{}".format(epoch, args.max_epochs - 1),
                "loss: {:.4f}".format(train_loss),
                "time {:.2f}s".format(time.time() - epoch_time),
            )
        if args.rank == 0 and writer is not None:
            writer.add_scalar("train_loss", train_loss, epoch)
        b_new_best = False
        if (epoch + 1) % args.val_every == 0:
            print('----------------------- model1 validation -------------------------')
            if args.distributed:
                torch.distributed.barrier()
            epoch_time = time.time()
            val_avg_acc_classes = val_epoch(
                model,
                val_loader,
                epoch=epoch,
                acc_func=acc_func,
                model_inferer=model_inferer,
                args=args,
                post_label=post_label,
                post_pred=post_pred,
            )
            print(val_avg_acc_classes)
            print(np.nanmean(val_avg_acc_classes))
            val_avg_acc = np.nanmean(val_avg_acc_classes)

            torch.cuda.empty_cache()
            gc.collect()
            if args.rank == 0:
                print(
                    "Final validation  {}/{}".format(epoch, args.max_epochs - 1),
                    "acc",
                    val_avg_acc,
                    "time {:.2f}s".format(time.time() - epoch_time),
                )
                if writer is not None:
                    writer.add_scalar("model1_val_dice", val_avg_acc, epoch)
                    writer.add_scalar("model1_val_dice_liver", val_avg_acc_classes[0], epoch)
                    writer.add_scalar("model1_val_dice_tumor", val_avg_acc_classes[1], epoch)
                if val_avg_acc > val_acc_max_model1:
                    print("new best ({:.6f} --> {:.6f}). ".format(val_acc_max_model1, val_avg_acc))
                    val_acc_max_model1 = val_avg_acc
                    b_new_best = True
                    if args.rank == 0 and args.logdir is not None:
                        save_checkpoint(
                            model, epoch, args, best_acc=val_acc_max_model1, optimizer=optimizer, optimizer2=optimizer2, scheduler=scheduler, filename="model1.pt"
                        )
            if args.rank == 0 and args.logdir is not None:
                save_checkpoint(model, epoch, args, best_acc=val_acc_max_model1, filename="model1_final.pt")

            # ----------------------------------------------------
            print('----------------------- model2 validation -------------------------')
            if args.distributed:
                torch.distributed.barrier()
            epoch_time = time.time()
            # 注意这里model_inferer里指定的model才是真正推理时用到的model
            val_avg_acc_classes = val_epoch(
                model2,
                val_loader,
                epoch=epoch,
                acc_func=acc_func,
                model_inferer=model_inferer2,
                args=args,
                post_label=post_label,
                post_pred=post_pred,
            )
            print(val_avg_acc_classes)
            print(np.nanmean(val_avg_acc_classes))
            val_avg_acc = np.nanmean(val_avg_acc_classes)

            torch.cuda.empty_cache()
            gc.collect()
            if args.rank == 0:
                print(
                    "Final validation  {}/{}".format(epoch, args.max_epochs - 1),
                    "acc",
                    val_avg_acc,
                    "time {:.2f}s".format(time.time() - epoch_time),
                )
                if writer is not None:
                    writer.add_scalar("model2_val_dice", val_avg_acc, epoch)
                    writer.add_scalar("model2_val_dice_liver", val_avg_acc_classes[0], epoch)
                    writer.add_scalar("model2_val_dice_tumor", val_avg_acc_classes[1], epoch)
                if val_avg_acc > val_acc_max_model2:
                    print("new best ({:.6f} --> {:.6f}). ".format(val_acc_max_model2, val_avg_acc))
                    val_acc_max_model2 = val_avg_acc
                    b_new_best = True
                    if args.rank == 0 and args.logdir is not None:
                        save_checkpoint(
                            model2, epoch, args, best_acc=val_acc_max_model2, optimizer=optimizer, optimizer2=optimizer2, scheduler=scheduler, filename="model2.pt"
                        )
            if args.rank == 0 and args.logdir is not None:
                save_checkpoint(model2, epoch, args, best_acc=val_acc_max_model2, filename="model2_final.pt")
                

        if scheduler is not None:
            scheduler.step()

    print("Training Finished !, Best Accuracy: ", max(val_acc_max_model1, val_acc_max_model2))

    return max(val_acc_max_model1, val_acc_max_model2)
