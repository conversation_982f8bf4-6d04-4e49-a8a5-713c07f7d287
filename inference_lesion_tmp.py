# Copyright 2020 - 2021 MONAI Consortium
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import argparse
import os
import time
import numpy as np
import torch
#
#

cpu_num = 1
os.environ['OMP_NUM_THREADS'] = str(cpu_num)
os.environ['OPENBLAS_NUM_THREADS'] = str(cpu_num)
os.environ['MKL_NUM_THREADS'] = str(cpu_num)
os.environ['VECLIB_MAXIMUM_THREADS'] = str(cpu_num)
os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_num)
torch.set_num_threads(cpu_num)
torch.multiprocessing.set_sharing_strategy('file_system')

import csv
import nibabel as nib
import SimpleITK as sitk
from torch.cuda.amp import GradScaler, autocast
from monai.inferers import sliding_window_inference
from monai.metrics import DiceMetric, HausdorffDistanceMetric, ConfusionMatrixMetric
from monai.transforms import Activations, AsDiscrete, Compose
from monai.utils.enums import MetricReduction
from functools import partial
from monai.data import decollate_batch
import scipy.ndimage as ndimage
import time
from medpy import metric
from networks.VNet import *
from factory import load_mae_p12_10k


from torch.utils.data import Dataset
from monai import data, transforms
from monai.data import load_decathlon_datalist



def build_model(model_name, out_channels, data_dir, ckpt_dir):
    if model_name == "load_mednext":
        model = load_mednext(out_channels)
    elif model_name == "load_unetr":
        model = load_unetr(out_channels)
    elif model_name == "load_vnet":
        model = VNet(n_channels=1, n_classes=out_channels, normalization='batchnorm', has_dropout=True).cuda()
    elif model_name == "load_mae" or model_name == "load_hi_end_mae_10k":
        model = load_mae_p12_10k(out_channels)
    
    
    ckpt = "./{}/{}_full_{}".format(ckpt_dir, model_name, data_dir.split("/")[-2])
    model_dict = torch.load(os.path.join(ckpt, "model.pt"))["state_dict"]
    model.load_state_dict(model_dict, strict=True)
    print(f"Load {ckpt}")
    return model, ckpt


def get_args(data):
    """
    Liver
    """
    if data == "liver":
        data_dir = "../data/Task03_Liver/"
        out_channels = 3
        ckpt = "/data2/fenghetang/eval_dice/MSD_Liver/lesion_runs/hyspark_contrast_large2_full_Dataset017_Liver_2mm"
    
    
    infer_overlap = 0.5
    return data_dir, out_channels, infer_overlap

def get_loader(data_dir, js):
    val_transform = transforms.Compose(
        [
            transforms.LoadImaged(keys=["image", "label"]),
            transforms.AddChanneld(keys=["image", "label"]),
            transforms.Orientationd(keys=["image", "label"], axcodes="RAS"),
            transforms.Spacingd(
                keys=["image", "label"], pixdim=(1.5, 1.5, 1.5), mode=("bilinear", "nearest")
            ),
            transforms.ScaleIntensityRanged(
                keys=["image"], a_min=-175, a_max=250, b_min=0, b_max=1, clip=True
            ),
            # 不要忘了删除之前的缓存，以及将后面的CacheNTransDataset的7改成6
            # transforms.CropForegroundd(keys=["image", "label"], source_key="image"),
            transforms.ToTensord(keys=["image", "label"]),
        ]
    )

    val_files = load_decathlon_datalist(os.path.join('.', js), True, "validation", base_dir=data_dir)
    val_ds = data.CacheNTransDataset(data=val_files, transform=val_transform, cache_n_trans=6, cache_dir="./3d_cache/cache/infer_{}".format(data_dir.split("/")[-2]))  
    val_loader = data.DataLoader(
        val_ds,
        batch_size=1,
        shuffle=False,
        num_workers=4,
        sampler=None,
        pin_memory=True,
    )
    return val_loader


def resample_3d(img, target_size):
    imx, imy, imz = img.shape
    tx, ty, tz = target_size
    zoom_ratio = (float(tx) / float(imx), float(ty) / float(imy), float(tz) / float(imz))
    img_resampled = ndimage.zoom(img, zoom_ratio, order=0, prefilter=False)
    return img_resampled


def calculate_metric(_pred, _gt):
    pred = np.zeros_like(_pred)
    gt = np.zeros_like(_gt)
    pred[_pred] = 1
    gt[_gt] = 1
    if pred.sum() > 0 and gt.sum() > 0:
        dice = metric.binary.dc(pred, gt)
        hd95 = metric.binary.hd95(pred, gt)
        asd = metric.binary.asd(pred, gt)
        return dice, hd95, asd
    elif pred.sum() > 0 and gt.sum() == 0:
        return 1, 0, 0
    else:
        return 0, 0, 0

def save_pred(val_outputs, original_affine, target_shape, path):
    val_outputs = torch.softmax(val_outputs, 1)
    val_outputs = torch.argmax(val_outputs, dim=1).type(torch.uint8)
    val_outputs = resample_3d(val_outputs[0], target_shape)
    print(val_outputs.shape)
    nib.save(nib.Nifti1Image(val_outputs.astype(np.uint8), original_affine), path)


def save_img(val_outputs, original_affine, target_shape, path):
    val_outputs = resample_3d(val_outputs[0][0].cpu().numpy(), target_shape)
    nib.save(nib.Nifti1Image(val_outputs, original_affine), path)


def inference():
    #pretrained_dir = pretrained_dir.replace("+", name).replace("amos", data_dir.split("/")[-2])
    ckpt_dir = "msd_liver_bs4"
    js = "json/lab.json"
    data_dir, out_channels, infer_overlap = get_args("liver")
    
    val_loader = get_loader(data_dir, js)
    
    post_label = AsDiscrete(to_onehot=out_channels, n_classes=out_channels)
    post_pred = AsDiscrete(argmax=True, to_onehot=out_channels, n_classes=out_channels)
    dice_metric = DiceMetric(include_background=False, reduction=MetricReduction.MEAN, get_not_nans=True)
    confmat_metric = ConfusionMatrixMetric(include_background=False, reduction="sum", get_not_nans=True)
    inf_size = (96, 96, 96)    
    

    #l = ["load_swinunetrv2", "load_spark", "load_mae_10k", "load_hi_end_mae_10k", "load_SUP","load_VoCo_10k", "load_VoCo_160k", "load_UniModel", "load_SuPreM"]
    l = [ "load_vnet"]
    for model_name in l: 
        model, ckpt = build_model(model_name, out_channels, data_dir, ckpt_dir)
        model.eval()
        model_inferer = partial(
            sliding_window_inference,
            roi_size=inf_size,
            sw_batch_size=1,
            predictor=model,
            overlap=infer_overlap,
        )
        val_case_per_dice = []
        val_case_per_hd = []
        val_case_per_asd = []
        case_name = []
        root = ckpt.split("/")[-1] + "_" + str(infer_overlap)
            
            
        with torch.no_grad():
            for i, batch in enumerate(val_loader):
                val_inputs, val_labels = (batch["image"].cuda(), batch["label"].cuda())
                h, w, d = batch["image_meta_dict"]["spatial_shape"][0]
                target_shape = (h, w, d)
                img_name = batch["image_meta_dict"]["filename_or_obj"][0].split("/")[-1]
                original_affine = batch["image_meta_dict"]["affine"][0].numpy()
                
                tmp = time.time()
                val_outputs = sliding_window_inference(val_inputs, inf_size, 1, model, overlap=infer_overlap)
                val_outputs = val_outputs.cpu()
                val_labels = val_labels.cpu()

                val_labels_list = decollate_batch(val_labels)
                val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]
                val_outputs_list = decollate_batch(val_outputs)
                
                # del val_outputs, val_labels
                # torch.cuda.empty_cache()
                
                val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]
                if True:
                    save_dir = "./{}/{}_full_{}/pred/".format(ckpt_dir, model_name, data_dir.split("/")[-2])
                    if not os.path.exists(save_dir):
                        os.makedirs(save_dir)
                    # if not os.path.exists("./result/{}/{}/{}/".format(args.model, name, args.data_dir.split("/")[-2]) + img_name):    
                    save_pred(val_outputs, original_affine, target_shape, "./{}/{}_full_{}/pred/".format(ckpt_dir, model_name, data_dir.split("/")[-2]) + img_name)


                acc = dice_metric(y_pred=val_output_convert, y=val_labels_convert)
                acc_list = acc.detach().numpy()[0]

                torch.cuda.empty_cache()
                
                
                print(f"Dice Coefficient (ignoring background): {acc_list}")
                
                
if __name__ == "__main__":
    inference()
