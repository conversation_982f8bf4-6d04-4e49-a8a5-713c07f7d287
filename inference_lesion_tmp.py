# Copyright 2020 - 2021 MONAI Consortium
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import argparse
import os
import time
import numpy as np
import torch
#
#

cpu_num = 1
os.environ['OMP_NUM_THREADS'] = str(cpu_num)
os.environ['OPENBLAS_NUM_THREADS'] = str(cpu_num)
os.environ['MKL_NUM_THREADS'] = str(cpu_num)
os.environ['VECLIB_MAXIMUM_THREADS'] = str(cpu_num)
os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_num)
torch.set_num_threads(cpu_num)
torch.multiprocessing.set_sharing_strategy('file_system')

import csv
import nibabel as nib
import SimpleITK as sitk
from torch.cuda.amp import GradScaler, autocast
from monai.inferers import sliding_window_inference
from monai.metrics import DiceMetric, HausdorffDistanceMetric, ConfusionMatrixMetric
from monai.transforms import Activations, AsDiscrete, Compose
from monai.utils.enums import MetricReduction
from functools import partial
from monai.data import decollate_batch
import scipy.ndimage as ndimage
import time
from medpy import metric
from networks.VNet import *
from factory import load_mae_p12_10k


from torch.utils.data import Dataset
from monai import data, transforms
from monai.data import load_decathlon_datalist



def build_model(model_name, out_channels, data_dir, ckpt_dir):
    if model_name == "load_mednext":
        model = load_mednext(out_channels)
    elif model_name == "load_unetr":
        model = load_unetr(out_channels)
    elif model_name == "load_vnet":
        model = VNet(n_channels=1, n_classes=out_channels, normalization='batchnorm', has_dropout=True).cuda()
    elif model_name == "load_mae" or model_name == "load_hi_end_mae_10k":
        model = load_mae_p12_10k(out_channels)
    
    
    ckpt = "./{}/{}_full_{}".format(ckpt_dir, model_name, data_dir.split("/")[-2])
    model_dict = torch.load(os.path.join(ckpt, "model.pt"))["state_dict"]
    model.load_state_dict(model_dict, strict=True)
    print(f"Load {ckpt}")
    return model, ckpt


def get_args(data):
    """
    Liver
    """
    if data == "liver":
        data_dir = "../data/Task03_Liver/"
        out_channels = 3
        ckpt = "/data2/fenghetang/eval_dice/MSD_Liver/lesion_runs/hyspark_contrast_large2_full_Dataset017_Liver_2mm"
    
    
    infer_overlap = 0.5
    return data_dir, out_channels, infer_overlap

def get_loader(data_dir, js):
    val_transform = transforms.Compose(
        [
            transforms.LoadImaged(keys=["image", "label"]),
            transforms.AddChanneld(keys=["image", "label"]),
            transforms.Orientationd(keys=["image", "label"], axcodes="RAS"),
            transforms.Spacingd(
                keys=["image", "label"], pixdim=(1.5, 1.5, 1.5), mode=("bilinear", "nearest")
            ),
            transforms.ScaleIntensityRanged(
                keys=["image"], a_min=-175, a_max=250, b_min=0, b_max=1, clip=True
            ),
            # 不要忘了删除之前的缓存，以及将后面的CacheNTransDataset的7改成6
            # transforms.CropForegroundd(keys=["image", "label"], source_key="image"),
            transforms.ToTensord(keys=["image", "label"]),
        ]
    )

    val_files = load_decathlon_datalist(os.path.join('.', js), True, "validation", base_dir=data_dir)
    val_ds = data.CacheNTransDataset(data=val_files, transform=val_transform, cache_n_trans=6, cache_dir="./3d_cache/cache/infer_{}".format(data_dir.split("/")[-2]))  
    val_loader = data.DataLoader(
        val_ds,
        batch_size=1,
        shuffle=False,
        num_workers=4,
        sampler=None,
        pin_memory=True,
    )
    return val_loader


def resample_3d(img, target_size):
    imx, imy, imz = img.shape
    tx, ty, tz = target_size
    zoom_ratio = (float(tx) / float(imx), float(ty) / float(imy), float(tz) / float(imz))
    img_resampled = ndimage.zoom(img, zoom_ratio, order=0, prefilter=False)
    return img_resampled


def calculate_metric(_pred, _gt):
    pred = np.zeros_like(_pred)
    gt = np.zeros_like(_gt)
    pred[_pred] = 1
    gt[_gt] = 1
    if pred.sum() > 0 and gt.sum() > 0:
        dice = metric.binary.dc(pred, gt)
        hd95 = metric.binary.hd95(pred, gt)
        asd = metric.binary.asd(pred, gt)
        return dice, hd95, asd
    elif pred.sum() > 0 and gt.sum() == 0:
        return 1, 0, 0
    else:
        return 0, 0, 0

def save_pred(val_outputs, original_affine, target_shape, path):
    val_outputs = torch.softmax(val_outputs, 1)
    val_outputs = torch.argmax(val_outputs, dim=1).type(torch.uint8)
    val_outputs = resample_3d(val_outputs[0], target_shape)
    print(val_outputs.shape)
    nib.save(nib.Nifti1Image(val_outputs.astype(np.uint8), original_affine), path)


def save_img(val_outputs, original_affine, target_shape, path):
    val_outputs = resample_3d(val_outputs[0][0].cpu().numpy(), target_shape)
    nib.save(nib.Nifti1Image(val_outputs, original_affine), path)


def calculate_tumor_statistics(pred_probs, pred_labels, gt_labels, case_name):
    """
    计算肿瘤区域的详细统计信息

    Args:
        pred_probs: 预测概率 (C, H, W, D) - softmax输出
        pred_labels: 预测标签 (H, W, D) - argmax后的标签
        gt_labels: 真实标签 (H, W, D)
        case_name: 病例名称

    Returns:
        dict: 包含各种统计信息的字典
    """
    # 获取肿瘤区域的mask (label=2)
    tumor_gt_mask = (gt_labels == 2)
    tumor_pred_mask = (pred_labels == 2)

    # 计算基本统计
    tumor_gt_voxels = np.sum(tumor_gt_mask)
    tumor_pred_voxels = np.sum(tumor_pred_mask)

    # 计算肿瘤区域的预测置信度
    if tumor_gt_voxels > 0:
        # 在真实肿瘤区域内的预测置信度
        tumor_confidence_in_gt = pred_probs[2][tumor_gt_mask]  # 肿瘤类别的概率
        mean_confidence_in_gt = np.mean(tumor_confidence_in_gt)
        std_confidence_in_gt = np.std(tumor_confidence_in_gt)
        min_confidence_in_gt = np.min(tumor_confidence_in_gt)
        max_confidence_in_gt = np.max(tumor_confidence_in_gt)

        # 计算不同置信度阈值下的统计
        high_conf_voxels = np.sum(tumor_confidence_in_gt > 0.8)
        medium_conf_voxels = np.sum((tumor_confidence_in_gt > 0.5) & (tumor_confidence_in_gt <= 0.8))
        low_conf_voxels = np.sum(tumor_confidence_in_gt <= 0.5)
    else:
        mean_confidence_in_gt = 0
        std_confidence_in_gt = 0
        min_confidence_in_gt = 0
        max_confidence_in_gt = 0
        high_conf_voxels = 0
        medium_conf_voxels = 0
        low_conf_voxels = 0

    # 计算预测准确性
    true_positive = np.sum(tumor_gt_mask & tumor_pred_mask)
    false_positive = np.sum((~tumor_gt_mask) & tumor_pred_mask)
    false_negative = np.sum(tumor_gt_mask & (~tumor_pred_mask))
    true_negative = np.sum((~tumor_gt_mask) & (~tumor_pred_mask))

    # 计算各种指标
    if tumor_gt_voxels > 0:
        sensitivity = true_positive / tumor_gt_voxels  # 召回率
        if tumor_pred_voxels > 0:
            precision = true_positive / tumor_pred_voxels
            dice_score = 2 * true_positive / (tumor_gt_voxels + tumor_pred_voxels)
        else:
            precision = 0
            dice_score = 0
    else:
        sensitivity = 0
        precision = 0
        dice_score = 0

    # 计算特异性
    total_non_tumor = np.sum(~tumor_gt_mask)
    if total_non_tumor > 0:
        specificity = true_negative / total_non_tumor
    else:
        specificity = 0

    # 计算F1分数
    if precision + sensitivity > 0:
        f1_score = 2 * (precision * sensitivity) / (precision + sensitivity)
    else:
        f1_score = 0

    statistics = {
        'case_name': case_name,
        'tumor_gt_voxels': int(tumor_gt_voxels),
        'tumor_pred_voxels': int(tumor_pred_voxels),
        'true_positive': int(true_positive),
        'false_positive': int(false_positive),
        'false_negative': int(false_negative),
        'true_negative': int(true_negative),
        'mean_confidence_in_gt': float(mean_confidence_in_gt),
        'std_confidence_in_gt': float(std_confidence_in_gt),
        'min_confidence_in_gt': float(min_confidence_in_gt),
        'max_confidence_in_gt': float(max_confidence_in_gt),
        'high_conf_voxels': int(high_conf_voxels),
        'medium_conf_voxels': int(medium_conf_voxels),
        'low_conf_voxels': int(low_conf_voxels),
        'sensitivity': float(sensitivity),
        'specificity': float(specificity),
        'precision': float(precision),
        'dice_score': float(dice_score),
        'f1_score': float(f1_score)
    }

    return statistics

def save_statistics_to_csv(all_statistics, model_name, data_dir, ckpt_dir):
    """保存统计结果到CSV文件"""
    save_dir = "./{}/{}_full_{}/".format(ckpt_dir, model_name, data_dir.split("/")[-2])
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    csv_path = os.path.join(save_dir, "tumor_statistics.csv")

    if all_statistics:
        fieldnames = all_statistics[0].keys()
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for stats in all_statistics:
                writer.writerow(stats)

        print(f"统计结果已保存到: {csv_path}")

def inference():
    #pretrained_dir = pretrained_dir.replace("+", name).replace("amos", data_dir.split("/")[-2])
    ckpt_dir = "msd_liver_bs4"
    js = "json/lab.json"
    data_dir, out_channels, infer_overlap = get_args("liver")

    val_loader = get_loader(data_dir, js)

    post_label = AsDiscrete(to_onehot=out_channels, n_classes=out_channels)
    post_pred = AsDiscrete(argmax=True, to_onehot=out_channels, n_classes=out_channels)
    dice_metric = DiceMetric(include_background=False, reduction=MetricReduction.MEAN, get_not_nans=True)
    confmat_metric = ConfusionMatrixMetric(include_background=False, reduction="sum", get_not_nans=True)
    inf_size = (96, 96, 96)


    #l = ["load_swinunetrv2", "load_spark", "load_mae_10k", "load_hi_end_mae_10k", "load_SUP","load_VoCo_10k", "load_VoCo_160k", "load_UniModel", "load_SuPreM"]
    l = [ "load_vnet"]
    for model_name in l:
        model, ckpt = build_model(model_name, out_channels, data_dir, ckpt_dir)
        model.eval()
        model_inferer = partial(
            sliding_window_inference,
            roi_size=inf_size,
            sw_batch_size=1,
            predictor=model,
            overlap=infer_overlap,
        )
        val_case_per_dice = []
        val_case_per_hd = []
        val_case_per_asd = []
        case_name = []
        all_tumor_statistics = []  # 存储所有病例的肿瘤统计信息
        root = ckpt.split("/")[-1] + "_" + str(infer_overlap)
            
            
        with torch.no_grad():
            for i, batch in enumerate(val_loader):
                val_inputs, val_labels = (batch["image"].cuda(), batch["label"].cuda())
                h, w, d = batch["image_meta_dict"]["spatial_shape"][0]
                target_shape = (h, w, d)
                img_name = batch["image_meta_dict"]["filename_or_obj"][0].split("/")[-1]
                original_affine = batch["image_meta_dict"]["affine"][0].numpy()

                tmp = time.time()
                val_outputs = sliding_window_inference(val_inputs, inf_size, 1, model, overlap=infer_overlap)

                # 获取原始的softmax概率输出
                val_outputs_softmax = torch.softmax(val_outputs, dim=1)
                val_outputs_cpu = val_outputs.cpu()
                val_outputs_softmax_cpu = val_outputs_softmax.cpu()
                val_labels_cpu = val_labels.cpu()

                val_labels_list = decollate_batch(val_labels_cpu)
                val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]
                val_outputs_list = decollate_batch(val_outputs_cpu)

                val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]

                # 计算肿瘤统计信息
                # 获取预测标签 (argmax)
                pred_labels = torch.argmax(val_outputs_cpu[0], dim=0).numpy()
                gt_labels = val_labels_cpu[0, 0].numpy()  # 假设标签是 (1, H, W, D) 格式
                pred_probs = val_outputs_softmax_cpu[0].numpy()  # (C, H, W, D)

                # 计算当前病例的肿瘤统计
                tumor_stats = calculate_tumor_statistics(pred_probs, pred_labels, gt_labels, img_name)
                all_tumor_statistics.append(tumor_stats)

                # 打印当前病例的统计信息
                print(f"\n=== 病例: {img_name} ===")
                print(f"肿瘤GT总voxel数: {tumor_stats['tumor_gt_voxels']}")
                print(f"肿瘤预测总voxel数: {tumor_stats['tumor_pred_voxels']}")
                print(f"预测正确的肿瘤voxel数: {tumor_stats['true_positive']}")
                print(f"肿瘤区域平均置信度: {tumor_stats['mean_confidence_in_gt']:.4f}")
                print(f"肿瘤区域置信度标准差: {tumor_stats['std_confidence_in_gt']:.4f}")
                print(f"高置信度voxel数 (>0.8): {tumor_stats['high_conf_voxels']}")
                print(f"中等置信度voxel数 (0.5-0.8): {tumor_stats['medium_conf_voxels']}")
                print(f"低置信度voxel数 (<=0.5): {tumor_stats['low_conf_voxels']}")
                print(f"敏感性 (召回率): {tumor_stats['sensitivity']:.4f}")
                print(f"精确率: {tumor_stats['precision']:.4f}")
                print(f"Dice分数: {tumor_stats['dice_score']:.4f}")
                print(f"F1分数: {tumor_stats['f1_score']:.4f}")

                # 保存预测结果
                if True:
                    save_dir = "./{}/{}_full_{}/pred/".format(ckpt_dir, model_name, data_dir.split("/")[-2])
                    if not os.path.exists(save_dir):
                        os.makedirs(save_dir)
                    save_pred(val_outputs_cpu, original_affine, target_shape, "./{}/{}_full_{}/pred/".format(ckpt_dir, model_name, data_dir.split("/")[-2]) + img_name)

                # 计算原有的Dice系数
                acc = dice_metric(y_pred=val_output_convert, y=val_labels_convert)
                acc_list = acc.detach().numpy()[0]

                torch.cuda.empty_cache()

                print(f"Dice Coefficient (ignoring background): {acc_list}")

        # 保存所有病例的统计结果到CSV
        save_statistics_to_csv(all_tumor_statistics, model_name, data_dir, ckpt_dir)

        # 计算总体统计
        if all_tumor_statistics:
            total_gt_voxels = sum([stats['tumor_gt_voxels'] for stats in all_tumor_statistics])
            total_pred_voxels = sum([stats['tumor_pred_voxels'] for stats in all_tumor_statistics])
            total_tp = sum([stats['true_positive'] for stats in all_tumor_statistics])
            total_fp = sum([stats['false_positive'] for stats in all_tumor_statistics])
            total_fn = sum([stats['false_negative'] for stats in all_tumor_statistics])

            overall_sensitivity = total_tp / total_gt_voxels if total_gt_voxels > 0 else 0
            overall_precision = total_tp / total_pred_voxels if total_pred_voxels > 0 else 0
            overall_dice = 2 * total_tp / (total_gt_voxels + total_pred_voxels) if (total_gt_voxels + total_pred_voxels) > 0 else 0

            mean_confidence = np.mean([stats['mean_confidence_in_gt'] for stats in all_tumor_statistics if stats['tumor_gt_voxels'] > 0])

            print(f"\n=== 总体统计 ({model_name}) ===")
            print(f"总病例数: {len(all_tumor_statistics)}")
            print(f"总肿瘤GT voxel数: {total_gt_voxels}")
            print(f"总预测正确voxel数: {total_tp}")
            print(f"总体敏感性: {overall_sensitivity:.4f}")
            print(f"总体精确率: {overall_precision:.4f}")
            print(f"总体Dice分数: {overall_dice:.4f}")
            print(f"平均置信度: {mean_confidence:.4f}")
            print("="*50)
                
                
if __name__ == "__main__":
    inference()
