import os
from models.unetr import UNETR as UNETR_p12

   
def load_mae_p12_10k(num_classes):
    model = UNETR_p12(
        in_channels=1,
        out_channels=num_classes,
        img_size=(96, 96, 96),
        feature_size=32,   # tom后来给的在MSD liver上训好的权重用48，本来的用32
        hidden_size=1536,
        mlp_dim=1536 * 4,
        num_heads=16,
        pos_embed="perceptron",
        norm_name="instance",
        conv_block=True,
        res_block=True,
        dropout_rate=0.0,
        qkv_bias=True
        )
    model_dict = torch.load("./mae_large_12p_10k.pth")['state_dict']
    pretrained_dict = {}
    pretrained_dict["vit.patch_embedding.patch_embeddings.1.weight"] = model_dict["encoder.patch_embed.proj.weight"].flatten(1)
    pretrained_dict["vit.patch_embedding.patch_embeddings.1.bias"] = model_dict["encoder.patch_embed.proj.bias"]
    pretrained_dict["vit.patch_embedding.position_embeddings"] = model_dict["encoder_pos_embed"]
    for k, v in model_dict.items():
        if "encoder." in k and "cls_token" not in k and "patch_embed" not in k:
            pretrained_dict[k.replace("encoder", "vit").replace("fc", "linear").replace("proj", "out_proj")] = v
    if model.load_state_dict(pretrained_dict, strict=False):
        print("mae_p12_10k Use pretrained weights {}".format("./mae_large_12p_10k.pth"))
    model.cuda()
    return model


import torch
if __name__ == '__main__':
    

    x = torch.rand((1, 1, 96, 96, 96)).cuda()
    model = load_mae_p12_10k(14)
    print(model(x).shape)
    #model = load_mask85(14)
    #print(model(x).shape)
    #model = load_mask90(14)
    #print(model(x).shape)
    #model = load_ablation_50(14)
    #print(model(x).shape)
    #model = load_ablation_20(14)
    #print(model(x).shape)
    #model = load_ablation_30(14)
    #print(model(x).shape)

