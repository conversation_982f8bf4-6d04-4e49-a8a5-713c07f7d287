# Copyright 2020 - 2021 MONAI Consortium
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import shutil
import time
import gc
import numpy as np
import torch
import torch.nn.parallel
import torch.utils.data.distributed
from tensorboardX import SummaryWriter
from torch.cuda.amp import GradScaler, autocast
from utils.utils import distributed_all_gather
import matplotlib.pyplot as plt

from monai.data import decollate_batch
from torchio import transforms as tiot
import random
from torch.nn import functional as F

def dice(x, y):
    intersect = np.sum(np.sum(np.sum(x * y)))
    y_sum = np.sum(np.sum(np.sum(y)))
    if y_sum == 0:
        return 0.0
    x_sum = np.sum(np.sum(np.sum(x)))
    return 2 * intersect / (x_sum + y_sum)


class AverageMeter(object):
    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = np.where(self.count > 0, self.sum / self.count, self.sum)

def sigmoid_rampup(current, rampup_length):
    """Exponential rampup from https://arxiv.org/abs/1610.02242"""
    if rampup_length == 0:
        return 1.0
    else:               
        current = np.clip(current, 0.0, rampup_length)
        phase = 1.0 - current / rampup_length
        return float(np.exp(-5.0 * phase * phase))

def get_current_consistency_weight(epoch):
    # Consistency ramp-up from https://arxiv.org/abs/1610.02242
    return 0.2 * sigmoid_rampup(epoch, 400)

def to_one_hot(tensor, nClasses):
    """ Input tensor : Nx1xHxW
    :param tensor:
    :param nClasses:
    :return:
    """
    assert tensor.max().item() < nClasses, 'one hot tensor.max() = {} < {}'.format(torch.max(tensor), nClasses)
    assert tensor.min().item() >= 0, 'one hot tensor.min() = {} < {}'.format(tensor.min(), 0)

    size = list(tensor.size())
    assert size[1] == 1
    size[1] = nClasses
    one_hot = torch.zeros(*size)
    if tensor.is_cuda:
        one_hot = one_hot.cuda(tensor.device)
    one_hot = one_hot.scatter_(1, tensor, 1)
    return one_hot


def get_probability(logits):
    """ Get probability from logits, if the channel of logits is 1 then use sigmoid else use softmax.
    :param logits: [N, C, H, W] or [N, C, D, H, W]
    :return: prediction and class num
    """
    size = logits.size()
    # N x 1 x H x W
    if size[1] > 1:
        pred = F.softmax(logits, dim=1)
        nclass = size[1]
    else:
        pred = F.sigmoid(logits)
        pred = torch.cat([1 - pred, pred], 1)
        nclass = 2
    return pred, nclass
def dice_loss(logits, target, mask=None):
    size = logits.size()
    N, nclass = size[0], size[1]

    logits = logits.view(N, nclass, -1)
    target = target.view(N, 1, -1)

    pred, nclass = get_probability(logits)

    # N x C x H x W
    pred_one_hot = pred
    target_one_hot = to_one_hot(target.type(torch.long), nclass).type(torch.float32)

    # N x C x H x W
    inter = pred_one_hot * target_one_hot
    union = pred_one_hot + target_one_hot

    if mask is not None:
        mask = mask.view(N, 1, -1)
        inter = (inter.view(N, nclass, -1) * mask).sum(2)
        union = (union.view(N, nclass, -1) * mask).sum(2)
    else:
        # N x C
        inter = inter.view(N, nclass, -1).sum(2)
        union = union.view(N, nclass, -1).sum(2)

    # smooth to prevent overfitting
    # [https://github.com/pytorch/pytorch/issues/1249]
    # NxC
    dice = (2 * inter + 1e-5) / (union + 1e-5)
    return 1 - dice.mean()

    # target = target.float()
    # smooth = 1e-5
    # intersect = torch.sum(score[ignore != 1] * target[ignore != 1])
    # y_sum = torch.sum(target[ignore != 1] * target[ignore != 1])
    # z_sum = torch.sum(score[ignore != 1] * score[ignore != 1])
    # loss = (2 * intersect + smooth) / (z_sum + y_sum + smooth)
    # loss = 1 - loss
    # return loss

def train_epoch(model, loader_lab, loader_unlab, optimizer, scaler, epoch, loss_func, args):
    model.train()
    start_time = time.time()
    run_loss = AverageMeter()
    
    consistency_weight = get_current_consistency_weight(epoch)
    
    for idx, (batch_data_lab, batch_data_unlab) in enumerate(zip(loader_lab, loader_unlab)):
        data_lab, target_lab = batch_data_lab["image"], batch_data_lab["label"]
        data_lab, target_lab = data_lab.cuda(args.rank), target_lab.cuda(args.rank)

        data_unlab = batch_data_unlab["image"]
        B_lab = data_lab.shape[0]
        B_unlab = data_unlab.shape[0]
        data_unlab_s1 = data_unlab.clone()
        data_unlab_s2 = data_unlab.clone()
        # 分别对 batch 内每个样本做 TorchIO 变换
        for i in range(B_unlab):
            img1 = data_unlab_s1[i]   # shape: (C, H, W, D)
            img2 = data_unlab_s2[i]

            if random.random() < 0.8:
                img1 = tiot.RandomBiasField()(img1)
                img1 = tiot.RandomGamma((-0.2, 0.2))(img1)
            if random.random() < 0.5:
                img1 = tiot.RandomBlur((0.1, 2))(img1)

            if random.random() < 0.8:
                img2 = tiot.RandomBiasField()(img2)
                img2 = tiot.RandomGamma((-0.2, 0.2))(img2)
            if random.random() < 0.5:
                img2 = tiot.RandomBlur((0.1, 2))(img2)

            # 放回去
            data_unlab_s1[i] = img1
            data_unlab_s2[i] = img2


        data_unlab = data_unlab.cuda(args.rank)
        data_unlab_s1 = data_unlab_s1.cuda(args.rank)
        data_unlab_s2 = data_unlab_s2.cuda(args.rank)
        data_combined = torch.cat([data_lab, data_unlab, data_unlab_s1, data_unlab_s2], dim=0)
        # print(data_lab.shape, data_unlab.shape, data_unlab_s1.shape, data_unlab_s2.shape)
        # print(data.shape)
        # print(batch_data_lab["image_meta_dict"]["filename_or_obj"])
        # input()
        for param in model.parameters():
            param.grad = None
        with autocast(enabled=args.amp):
            logits = model(data_combined)
            # print(logits.shape)
            logits_soft = torch.softmax(logits, dim=1)
            pseudo_label1 = torch.argmax(logits_soft, dim=1)
            logits_lab, logits_unlab, logits_unlab_s1, logits_unlab_s2 = torch.split(
                logits, [B_lab, B_unlab, B_unlab, B_unlab], dim=0
            )
            soft_lab, soft_unlab, soft_unlab_s1, soft_unlab_s2 = torch.split(
                logits_soft, [B_lab, B_unlab, B_unlab, B_unlab], dim=0
            )
            pseudo_lab, pseudo_unlab, pseudo_unlab_s1, pseudo_unlab_s2 = torch.split(
                pseudo_label1, [B_lab, B_unlab, B_unlab, B_unlab], dim=0
            )
            conf_u = soft_unlab.max(dim=1)[0]    # torch.Size([4, 96, 96, 96])
            
            loss1 = loss_func(logits_lab, target_lab)
            loss_u_s1 = dice_loss(soft_unlab_s1, pseudo_unlab, mask=(conf_u > 0.75).long())
            loss_u_s2 = dice_loss(soft_unlab_s2, pseudo_unlab, mask=(conf_u > 0.75).long())

            loss = loss1 + consistency_weight * (loss_u_s1 + loss_u_s2)
            print("loss: {:.4f}, loss1: {:.4f}, loss_u_s1: {:.4f}, loss_u_s2: {:.4f},".format(loss.item(), loss1.item(), loss_u_s1.item(), loss_u_s2.item()))

            if True:
                # 可视化
                slice_unlab    = data_unlab[0, 0, :, :, 10].cpu().numpy()
                slice_unlab_s1 = data_unlab_s1[0, 0, :, :, 10].cpu().numpy()
                slice_unlab_s2 = data_unlab_s2[0, 0, :, :, 10].cpu().numpy()
                pseudo_unlab = pseudo_unlab[0, :, :, 10].cpu().numpy()
                print(pseudo_unlab)
                import imageio
                def normalize_uint8(img):
                    img = img - img.min()
                    img = img / (img.max() + 1e-8)  # 防止除 0
                    img = (img * 255).astype(np.uint8)
                    return img

                slice_unlab    = normalize_uint8(slice_unlab)
                slice_unlab_s1 = normalize_uint8(slice_unlab_s1)
                slice_unlab_s2 = normalize_uint8(slice_unlab_s2)
                imageio.imwrite(os.path.join('.', "unlab.png"), slice_unlab)
                imageio.imwrite(os.path.join('.', "unlab_s1.png"), slice_unlab_s1)
                imageio.imwrite(os.path.join('.', "unlab_s2.png"), slice_unlab_s2)
                
                print('zzzzzzzzzzzzzzz')
                input()

        if args.amp:
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            loss.backward()
            optimizer.step()
        if args.distributed:
            loss_list = distributed_all_gather([loss], out_numpy=True, is_valid=idx < loader_lab.sampler.valid_length)
            run_loss.update(
                np.mean(np.mean(np.stack(loss_list, axis=0), axis=0), axis=0), n=args.batch_size * args.world_size
            )
        else:
            run_loss.update(loss.item(), n=args.batch_size)
        
        # if idx % 200 == 0 and args.rank == 0:
        #     print(
        #         "Iteration {}/{}".format(idx, args.max_epochs * 5000),
        #         "loss: {:.4f}".format(run_loss.avg),
        #         "time {:.2f}s".format(time.time() - start_time),
        #     )
        
    if args.rank == 0:
        print(
            "Epoch {}/{}".format(epoch, args.max_epochs),
            "loss: {:.4f}".format(run_loss.avg),
            "time {:.2f}s".format(time.time() - start_time),
        )
    start_time = time.time()
    for param in model.parameters():
        param.grad = None
    return run_loss.avg


def val_epoch(model, loader, epoch, acc_func, args, model_inferer=None, post_label=None, post_pred=None):
    model.eval()
    start_time = time.time()
    val_dice = []
    with torch.no_grad():
        for idx, batch_data in enumerate(loader):
            if isinstance(batch_data, list):
                data, target = batch_data
            else:
                data, target = batch_data["image"], batch_data["label"]
            data, target = data.cuda(args.rank), target.cuda(args.rank)
            img_name = batch_data["image_meta_dict"]["filename_or_obj"][0].split("/")[-1]
            with autocast(enabled=args.amp):
                if model_inferer is not None:
                    logits = model_inferer(data)
                else:
                    logits = model(data)
            if not logits.is_cuda:
                target = target.cpu()
            val_labels_list = decollate_batch(target)
            val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]
            val_outputs_list = decollate_batch(logits)
            val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]
            acc = acc_func(y_pred=val_output_convert, y=val_labels_convert)
            acc = acc.cuda(args.rank)

            if args.distributed:
                acc_list = distributed_all_gather([acc], out_numpy=True, is_valid=idx < loader.sampler.valid_length)
                avg_acc = np.mean([np.nanmean(l) for l in acc_list])

            else:
                acc_list = acc.detach().cpu().numpy()
                avg_acc = np.mean([np.nanmean(l) for l in acc_list])
                print(acc_list)
                # input()
            #print(img_name, acc_list[0] * 100)
            if args.rank == 0:
                print(
                    "Val {}/{} {}/{}".format(epoch, args.max_epochs, idx, len(loader)),
                    "acc",
                    avg_acc,
                    "time {:.2f}s".format(time.time() - start_time),
                )
            val_dice.extend(acc_list)
            
            del logits, val_output_convert, val_labels_convert, acc
            torch.cuda.empty_cache()
            
            start_time = time.time()
    if args.distributed:
        flat = [arr for sublist in val_dice for arr in sublist]
        val_dice = np.vstack(flat)
    print(val_dice)
    return np.nanmean(val_dice, axis=0)


def save_checkpoint(model, epoch, args, filename="model.pt", best_acc=0, optimizer=None, scheduler=None):
    state_dict = model.state_dict() if not args.distributed else model.module.state_dict()
    save_dict = {"epoch": epoch, "best_acc": best_acc, "state_dict": state_dict}
    if optimizer is not None:
        save_dict["optimizer"] = optimizer.state_dict()
    if scheduler is not None:
        save_dict["scheduler"] = scheduler.state_dict()
    filename = os.path.join(args.logdir, filename)
    torch.save(save_dict, filename)
    print("Saving checkpoint", filename)


def run_training_semi(
    model,
    train_loader_lab,
    train_loader_unlab,
    val_loader,
    optimizer,
    loss_func,
    acc_func,
    args,
    model_inferer=None,
    scheduler=None,
    start_epoch=0,
    post_label=None,
    post_pred=None,
):
    writer = None
    if args.logdir is not None and args.rank == 0:
        writer = SummaryWriter(log_dir=args.logdir)
        if args.rank == 0:
            print("Writing Tensorboard logs to ", args.logdir)
    scaler = None
    if args.amp:
        scaler = GradScaler()
    val_acc_max_model1 = 0.0
    for epoch in range(start_epoch, args.max_epochs):
        if args.distributed:
            train_loader_lab.sampler.set_epoch(epoch)
            train_loader_unlab.sampler.set_epoch(epoch)
            torch.distributed.barrier()
        #print(args.rank, time.ctime(), "Epoch:", epoch)
        epoch_time = time.time()
        train_loss = train_epoch(
            model, train_loader_lab, train_loader_unlab, optimizer, scaler=scaler, epoch=epoch, loss_func=loss_func, args=args
        )
        if args.rank == 0:
            print(
                "Final training  {}/{}".format(epoch, args.max_epochs - 1),
                "loss: {:.4f}".format(train_loss),
                "time {:.2f}s".format(time.time() - epoch_time),
            )
        if args.rank == 0 and writer is not None:
            writer.add_scalar("train_loss", train_loss, epoch)
        b_new_best = False
        if (epoch + 1) % args.val_every == 0:
            if args.distributed:
                torch.distributed.barrier()
            epoch_time = time.time()
            val_avg_acc_classes = val_epoch(
                model,
                val_loader,
                epoch=epoch,
                acc_func=acc_func,
                model_inferer=model_inferer,
                args=args,
                post_label=post_label,
                post_pred=post_pred,
            )
            print(val_avg_acc_classes)
            print(np.nanmean(val_avg_acc_classes))
            val_avg_acc = np.nanmean(val_avg_acc_classes)

            torch.cuda.empty_cache()
            gc.collect()
            if args.rank == 0:
                print(
                    "Final validation  {}/{}".format(epoch, args.max_epochs - 1),
                    "acc",
                    val_avg_acc,
                    "time {:.2f}s".format(time.time() - epoch_time),
                )
                if writer is not None:
                    writer.add_scalar("model1_val_dice", val_avg_acc, epoch)
                    writer.add_scalar("model1_val_dice_liver", val_avg_acc_classes[0], epoch)
                    writer.add_scalar("model1_val_dice_tumor", val_avg_acc_classes[1], epoch)
                if val_avg_acc > val_acc_max_model1:
                    print("new best ({:.6f} --> {:.6f}). ".format(val_acc_max_model1, val_avg_acc))
                    val_acc_max_model1 = val_avg_acc
                    b_new_best = True
                    if args.rank == 0 and args.logdir is not None:
                        save_checkpoint(
                            model, epoch, args, best_acc=val_acc_max_model1, optimizer=optimizer, scheduler=scheduler, filename="model1.pt"
                        )
            if args.rank == 0 and args.logdir is not None:
                save_checkpoint(model, epoch, args, best_acc=val_acc_max_model1, filename="model1_final.pt")

        if scheduler is not None:
            scheduler.step()

    print("Training Finished !, Best Accuracy: ", val_acc_max_model1)

    return val_acc_max_model1
