# 肝脏肿瘤分割模型推理与统计分析

## 概述

本代码对原有的肝脏肿瘤分割推理代码进行了增强，添加了详细的肿瘤区域预测置信度和准确性统计功能。

## 主要功能

### 1. 肿瘤区域统计分析

对每个验证数据，代码会计算以下统计信息：

#### 基本统计
- **肿瘤GT总voxel数**: Ground Truth中标签为2（肿瘤）的体素总数
- **肿瘤预测总voxel数**: 模型预测为肿瘤的体素总数
- **预测正确的肿瘤voxel数**: 真正例（True Positive）的数量

#### 置信度分析
- **肿瘤区域平均置信度**: 在GT肿瘤区域内，模型对肿瘤类别预测的平均概率
- **肿瘤区域置信度标准差**: 置信度的标准差
- **最小/最大置信度**: 置信度的范围
- **高置信度voxel数**: 置信度 > 0.8 的体素数量
- **中等置信度voxel数**: 置信度在 0.5-0.8 之间的体素数量
- **低置信度voxel数**: 置信度 ≤ 0.5 的体素数量

#### 准确性指标
- **敏感性（召回率）**: TP / (TP + FN) - 模型检测到的真实肿瘤比例
- **特异性**: TN / (TN + FP) - 模型正确识别非肿瘤区域的比例
- **精确率**: TP / (TP + FP) - 预测为肿瘤的区域中真正是肿瘤的比例
- **Dice分数**: 2*TP / (GT + Pred) - 分割重叠度
- **F1分数**: 精确率和召回率的调和平均

### 2. 输出格式

#### 控制台输出
每个病例处理完成后，会在控制台显示详细的统计信息：

```
=== 病例: liver_001.nii.gz ===
肿瘤GT总voxel数: 1523
肿瘤预测总voxel数: 1456
预测正确的肿瘤voxel数: 1234
肿瘤区域平均置信度: 0.7823
肿瘤区域置信度标准差: 0.1456
高置信度voxel数 (>0.8): 892
中等置信度voxel数 (0.5-0.8): 531
低置信度voxel数 (<=0.5): 100
敏感性 (召回率): 0.8103
精确率: 0.8475
Dice分数: 0.8285
F1分数: 0.8285
```

#### CSV文件输出
所有病例的统计结果会保存到CSV文件中，路径为：
```
./{ckpt_dir}/{model_name}_full_{dataset_name}/tumor_statistics.csv
```

CSV文件包含以下列：
- case_name: 病例名称
- tumor_gt_voxels: GT肿瘤体素数
- tumor_pred_voxels: 预测肿瘤体素数
- true_positive: 真正例数量
- false_positive: 假正例数量
- false_negative: 假负例数量
- true_negative: 真负例数量
- mean_confidence_in_gt: 平均置信度
- std_confidence_in_gt: 置信度标准差
- min_confidence_in_gt: 最小置信度
- max_confidence_in_gt: 最大置信度
- high_conf_voxels: 高置信度体素数
- medium_conf_voxels: 中等置信度体素数
- low_conf_voxels: 低置信度体素数
- sensitivity: 敏感性
- specificity: 特异性
- precision: 精确率
- dice_score: Dice分数
- f1_score: F1分数

#### 总体统计
所有病例处理完成后，会显示总体统计信息：

```
=== 总体统计 (load_vnet) ===
总病例数: 20
总肿瘤GT voxel数: 25430
总预测正确voxel数: 20344
总体敏感性: 0.8000
总体精确率: 0.8500
总体Dice分数: 0.8235
平均置信度: 0.7654
```

## 使用方法

1. 确保所有依赖库已安装（numpy, torch, monai等）
2. 运行推理代码：
   ```bash
   python inference_lesion_tmp.py
   ```
3. 查看控制台输出获取实时统计信息
4. 检查生成的CSV文件获取详细数据

## 代码修改说明

### 新增函数

1. **calculate_tumor_statistics()**: 计算单个病例的肿瘤统计信息
2. **save_statistics_to_csv()**: 将统计结果保存到CSV文件

### 主要修改

1. 在推理循环中添加了softmax概率计算
2. 对每个病例计算详细的肿瘤统计信息
3. 添加了实时的统计信息显示
4. 添加了CSV文件保存功能
5. 添加了总体统计计算和显示

## 注意事项

1. 代码假设标签格式为：0=背景，1=肝脏，2=肿瘤
2. 置信度分析基于softmax输出的概率值
3. 统计计算在CPU上进行，避免GPU内存问题
4. CSV文件会自动创建保存目录

## 测试

可以运行测试脚本验证功能：
```bash
python test_tumor_stats.py
```

测试脚本会验证统计计算和CSV保存功能是否正常工作。
